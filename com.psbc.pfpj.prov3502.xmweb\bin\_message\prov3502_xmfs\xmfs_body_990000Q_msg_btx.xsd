<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmfs.xmfs_body_990000Q" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmfs.xmfs_body_990000Q">
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmfs_body_990000Q">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
