<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmfs.xmfs_body_990000R" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmfs.xmfs_body_990000R">
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="rescode" nillable="true" type="xs:string"/>
            <xs:element name="resmsg" nillable="true" type="xs:string"/>
            <xs:element name="resseqno" nillable="true" type="xs:string"/>
            <xs:element name="sendseqno" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmfs_body_990000R">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
