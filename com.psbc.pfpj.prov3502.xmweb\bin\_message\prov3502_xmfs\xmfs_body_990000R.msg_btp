<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="message" create-date="2021-09-06 10:08:59" version="7.0.0.0">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="xmfs_body_990000R" name="xmfs_body_990000R" namespace="prov3502_xmfs.xmfs_body_990000R" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <group-message-item display-name="body" name="body" seqno="0" xml-prefix="">
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="RET_CODE" display-name="响应代码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="rescode" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="RET_EXPLAIN" display-name="响应消息" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="resmsg" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR45" display-name="响应方流水号" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="resseqno" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR44" display-name="发送方流水号" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="sendseqno" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>